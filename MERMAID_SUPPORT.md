# Mermaid图表支持

markdown-livesync插件现在支持Mermaid图表渲染！

## 功能特性

- ✅ 支持所有主要的Mermaid图表类型
- ✅ 实时预览和同步
- ✅ 错误处理和调试信息
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 与现有功能完全兼容
- ✅ 优化的布局居中（修复目录显示时的内容对齐问题）
- ✅ 智能图表尺寸控制（100%宽度，高度自适应）

## 支持的图表类型

### 1. 流程图 (Flowchart)
```mermaid
graph TD
    A[开始] --> B{条件判断}
    B -->|是| C[执行操作]
    B -->|否| D[跳过]
    C --> E[结束]
    D --> E
```

### 2. 序列图 (Sequence Diagram)
```mermaid
sequenceDiagram
    participant A as 客户端
    participant B as 服务器
    A->>B: 发送请求
    B-->>A: 返回响应
```

### 3. 甘特图 (Gantt Chart)
```mermaid
gantt
    title 项目计划
    dateFormat YYYY-MM-DD
    section 开发
    任务1 :done, task1, 2024-01-01, 2024-01-15
    任务2 :active, task2, 2024-01-16, 2024-01-30
```

### 4. 类图 (Class Diagram)
```mermaid
classDiagram
    class Animal {
        +String name
        +makeSound()
    }
    class Dog {
        +bark()
    }
    Animal <|-- Dog
```

### 5. 状态图 (State Diagram)
```mermaid
stateDiagram-v2
    [*] --> 待机
    待机 --> 运行 : 启动
    运行 --> 待机 : 停止
    运行 --> [*]
```

### 6. 饼图 (Pie Chart)
```mermaid
pie title 数据分布
    "A" : 42.96
    "B" : 50.05
    "C" : 7.01
```

## 使用方法

1. 在Markdown文档中使用标准的代码块语法
2. 指定语言为 `mermaid`
3. 在代码块中编写Mermaid图表代码

示例：
````markdown
```mermaid
graph LR
    A --> B
    B --> C
```
````

## 技术实现

### 后端处理
- 新增 `mermaidPlugin.ts` 插件
- 识别 `mermaid` 代码块并转换为特殊HTML结构
- 使用 `data-mermaid` 属性存储图表代码，避免HTML转义问题

### 前端渲染
- 集成 Mermaid.js 库 (v10.6.1)
- 自动检测和渲染Mermaid图表
- 错误处理和用户友好的错误显示

### 样式设计
- 响应式图表容器
- 统一的视觉风格
- 错误状态的清晰显示

## 错误处理

如果Mermaid图表语法有误，插件会显示友好的错误信息：

- 错误原因说明
- 原始代码显示
- 调试信息

## 兼容性

- 与现有的markdown-livesync功能完全兼容
- 支持行号同步和光标跟踪
- 目录导航正常工作
- 实时更新功能正常

## 配置

无需额外配置，Mermaid支持开箱即用。

## 依赖项

新增依赖：
- `mermaid`: ^10.6.1 (通过CDN加载)

## 测试

使用提供的 `test-mermaid.md` 文件测试各种图表类型的渲染效果。

## 故障排除

### 图表不显示
1. 检查代码块语言是否为 `mermaid`
2. 验证Mermaid语法是否正确
3. 查看浏览器控制台是否有错误信息

### 渲染错误
1. 检查Mermaid.js库是否正确加载
2. 验证网络连接（CDN加载）
3. 查看错误详情和原始代码

## 更新日志

### v0.1.56 - 修复版本
- ✅ 添加Mermaid图表支持
- ✅ 集成Mermaid.js v10.6.1
- ✅ 实现错误处理机制
- ✅ 添加响应式样式
- ✅ 完善文档和测试用例
- 🔧 **修复布局问题**：改用flexbox布局，确保内容在有目录时正确居中
- 🔧 **修复图表尺寸问题**：
  - 图表宽度调整为100%，提供更好的视觉效果
  - 禁用Mermaid的useMaxWidth限制，让CSS控制尺寸
  - 实现真正的高度自适应，移除固定高度限制
  - 添加响应式设计，在不同屏幕尺寸下优化显示
