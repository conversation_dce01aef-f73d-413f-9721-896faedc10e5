/**
 * MarkdownIt Mermaid插件
 *
 * 这个插件识别Markdown中的Mermaid代码块，并将其转换为特殊的HTML结构，
 * 以便在前端使用Mermaid.js进行渲染。
 */

import MarkdownIt = require('markdown-it');

/**
 * 为 MarkdownIt 添加Mermaid插件
 *
 * @param md MarkdownIt 实例
 */
export function mermaidPlugin(md: MarkdownIt): void {
  // 保存原始的fence规则
  const originalFence = md.renderer.rules.fence;

  // 重写fence规则以处理Mermaid代码块
  md.renderer.rules.fence = (tokens: any[], idx: number, options: any, env: any, self: any) => {
    const token = tokens[idx];
    const info = token.info ? token.info.trim() : '';
    const langName = info.split(/\s+/g)[0];

    // 检查是否是Mermaid代码块
    if (langName === 'mermaid') {
      // 生成唯一的ID
      const mermaidId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 获取Mermaid代码内容
      const mermaidCode = token.content.trim();
      
      // 创建特殊的HTML结构用于Mermaid渲染
      // 使用data-mermaid属性存储Mermaid代码，避免HTML转义问题
      const html = `<div class="mermaid-container" data-line="${token.map ? token.map[0] + 1 : 1}">
  <div id="${mermaidId}" class="mermaid" data-mermaid="${encodeURIComponent(mermaidCode)}">
    ${md.utils.escapeHtml(mermaidCode)}
  </div>
</div>`;

      return html;
    }

    // 如果不是Mermaid代码块，使用原始规则
    if (originalFence) {
      return originalFence(tokens, idx, options, env, self);
    }

    // 如果没有原始规则，使用默认处理
    return self.renderToken(tokens, idx, options);
  };
}
