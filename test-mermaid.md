# Mermaid图表测试

这是一个测试Mermaid图表渲染功能的文档。

## 流程图

下面是一个简单的流程图：

```mermaid
graph TD
    A[开始] --> B{是否有数据?}
    B -->|是| C[处理数据]
    B -->|否| D[获取数据]
    C --> E[显示结果]
    D --> C
    E --> F[结束]
```

## 序列图

这是一个序列图示例：

```mermaid
sequenceDiagram
    participant A as 用户
    participant B as 前端
    participant C as 后端
    participant D as 数据库

    A->>B: 发送请求
    B->>C: 转发请求
    C->>D: 查询数据
    D-->>C: 返回数据
    C-->>B: 返回响应
    B-->>A: 显示结果
```

## 甘特图

项目进度甘特图：

```mermaid
gantt
    title 项目开发进度
    dateFormat  YYYY-MM-DD
    section 设计阶段
    需求分析           :done,    des1, 2024-01-01,2024-01-05
    UI设计            :done,    des2, 2024-01-06, 2024-01-12
    section 开发阶段
    前端开发          :active,  dev1, 2024-01-13, 2024-02-15
    后端开发          :         dev2, 2024-01-20, 2024-02-20
    section 测试阶段
    单元测试          :         test1, after dev1, 5d
    集成测试          :         test2, after dev2, 3d
```

## 类图

简单的类图示例：

```mermaid
classDiagram
    class MarkdownProcessor {
        -md: MarkdownIt
        +constructor()
        +convertToHtml(markdown: string): string
        +generateToc(markdown: string): TocItem[]
        +configurePlugins(): void
    }
    
    class MermaidPlugin {
        +mermaidPlugin(md: MarkdownIt): void
    }
    
    MarkdownProcessor --> MermaidPlugin : uses
```

## 状态图

用户登录状态图：

```mermaid
stateDiagram-v2
    [*] --> 未登录
    未登录 --> 登录中 : 点击登录
    登录中 --> 已登录 : 登录成功
    登录中 --> 未登录 : 登录失败
    已登录 --> 未登录 : 退出登录
    已登录 --> [*]
```

## 饼图

数据分布饼图：

```mermaid
pie title 编程语言使用分布
    "JavaScript" : 35
    "Python" : 25
    "Java" : 20
    "TypeScript" : 15
    "其他" : 5
```

这些图表应该能够正确渲染并显示在预览中。
