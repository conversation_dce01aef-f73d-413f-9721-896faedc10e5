/* Markdown预览样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

/* 容器布局 - 完全重写 */
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 目录导航样式 - 使用固定宽度并固定在左侧 */
#toc-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 100vh;
  background-color: #f5f5f5;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  transition: transform 0.3s ease;
  z-index: 100;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* 内容容器样式 - 关键修改 */
#content-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: center; /* 使用flexbox居中，而不是text-align */
  align-items: flex-start; /* 顶部对齐 */
}

/* 当目录显示时，确保内容不被遮挡 */
.container.with-toc #content-container {
  padding-left: 270px; /* 250px目录宽度 + 20px内边距 */
}

#toc-container.hidden {
  transform: translateX(-100%);
}

.toc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f0f0f0;
}

.toc-header h3 {
  margin: 0;
  font-size: 16px;
}

#toggle-toc {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
}

#toggle-toc:hover {
  color: #000;
}

#toc-content {
  padding: 10px 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 目录控制面板 */
#toc-controls {
  padding: 4px 4px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* 文章标题容器 */
#toc-title {
  width: 100%;
  text-align: center;
  margin-bottom: 1px;
}

/* 文章标题样式 */
.article-title {
  font-size: 1.5em;
  margin: 0;
  padding: 5px 0;
  color: #333;
  border-bottom: 1px solid #eaecef;
}

#toc-level-control {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
  margin-top: 4px; /* 调整上边距为10px */
  margin-bottom: 0px; /* 调整下边距为10px */
  gap: 6px; /* 稍微增加按钮间距 */
}

.toc-level-button {
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 28px; /* 20px * 1.4 = 28px */
  height: 17px; /* 12px * 1.4 ≈ 17px */
  padding: 0;
  margin: 0;
  cursor: pointer;
  font-size: 12px; /* 增大字体 */
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toc-level-button:hover {
  background: #e0e0e0;
  transform: scale(1.1);
}

.toc-level-button.active {
  background: #0366d6;
  color: white;
  border-color: #0366d6;
  transform: scale(1.1);
}

/* 特殊处理"All"按钮 */
.toc-level-button[data-level="-1"] {
  border-radius: 10px;
  width: 40px; /* 28px * 1.4 ≈ 40px */
  font-size: 11px;
}

#toc-content ul {
  list-style-type: none;
  padding-left: 0px;
  margin: 0;
}

#toc-content li {
  margin: 6px 0;
  display: flex;
  align-items: center; /* 改为居中对齐 */
  line-height: 1.4;
}

#toc-content a {
  text-decoration: none;
  color: #0366d6;
  display: block;
  padding: 3px 0;
  border-radius: 3px;
  flex: 1;
}

/* 一级标题样式 - 包括原始一级标题和调整后的一级标题（原二级标题） */
#toc-content a[data-level="1"] {
  font-weight: bold;
  font-size: 1.1em;
  color: #000;
  margin-top: 8px;
  margin-bottom: 4px;
}

/* 二级标题样式 */
#toc-content a[data-level="2"] {
  font-weight: normal;
  color: #333;
  margin-top: 4px;
}

#toc-content a:hover {
  text-decoration: underline;
  color: #0056b3;
}

/* 目录展开/折叠按钮样式 */
.toc-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  text-align: center;
  font-size: 9px;
  margin-right: 5px;
  cursor: pointer;
  color: #666;
  user-select: none;
}

.toc-toggle:hover {
  color: #000;
}

.toc-toggle-placeholder {
  display: inline-block;
  width: 14px;
  margin-right: 5px;
}

/* 目录子列表样式 */
.toc-sublist {
  margin-left: 0 !important;
  padding-left: 8px !important;
  border-left: 1px solid #eaecef;
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

/* Markdown内容容器样式 */
#markdown-content {
  width: 100%; /* 使用全宽 */
  max-width: 1100px; /* 最大宽度限制 */
  transition: all 0.3s ease;
  text-align: left; /* 确保内容本身是左对齐的 */
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  padding: 0 20px; /* 添加左右内边距 */
  flex-shrink: 0; /* 防止在flex容器中收缩 */
}

/* 工具栏样式 */
.toolbar {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 150; /* 确保在目录之上 */
  transition: all 0.3s ease;
}

/* 当目录隐藏时，工具栏显示在左侧 */
.container:not(.with-toc) .toolbar {
  left: 10px;
}

/* 当目录显示时，工具栏隐藏（因为目录中有关闭按钮） */
.container.with-toc .toolbar {
  display: none;
}

#show-toc {
  background: rgba(240, 240, 240, 0.8);
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 16px;
}

#show-toc:hover {
  background: rgba(220, 220, 220, 0.9);
}

#show-toc.hidden {
  display: none;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

h1 {
  font-size: 2em;
  border-bottom: 1px solid #23ce45;
  padding-bottom: 0.3em;
}

h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

h3 {
  font-size: 1.25em;
}

/* 链接样式 */
a {
  color: #0366d6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 代码样式 */
code {
  font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(27, 31, 35, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 85%;
}

pre {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

table th, table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* 引用样式 */
blockquote {
  margin: 0;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

/* 图片样式 */
img {
  max-width: 100%;
  box-sizing: border-box;
}

/* Mermaid图表样式 */
.mermaid-container {
  margin: 16px 0;
  text-align: center;
  background-color: #f8f9fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 20px;
  overflow-x: auto;
  min-height: 200px; /* 设置最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.mermaid {
  display: inline-block;
  width: 100%; /* 设置宽度为100% */
  max-width: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 150px; /* 设置最小高度 */
}

.mermaid svg {
  width: 100% !important; /* 强制SVG使用容器的100%宽度 */
  height: auto !important; /* 高度自适应 */
  max-width: none !important; /* 移除最大宽度限制 */
  display: block;
  margin: 0 auto;
}

/* Mermaid错误样式 */
.mermaid-error {
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 16px;
  color: #c53030;
  text-align: left;
}

.mermaid-error p {
  margin: 0 0 8px 0;
  font-weight: bold;
}

.mermaid-error pre {
  background-color: #fed7d7;
  border: none;
  padding: 8px;
  margin: 8px 0;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.mermaid-error details {
  margin-top: 8px;
}

.mermaid-error summary {
  cursor: pointer;
  font-weight: bold;
  margin-bottom: 8px;
}

.mermaid-error summary:hover {
  color: #9b2c2c;
}

/* 高亮当前行 */
.highlight-line {
  background-color: #fffbdd !important;
  animation: highlight-pulse 2s ease-out infinite;
  position: relative;
  z-index: 5;
  border-radius: 3px;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
  border-left: 3px solid #ff9800 !important;
  padding-left: 10px !important;
  margin-left: -10px !important;
}

@keyframes highlight-fade {
  from { background-color: #fffbdd; }
  to { background-color: transparent; }
}

@keyframes highlight-pulse {
  0% { box-shadow: 0 0 5px rgba(255, 152, 0, 0.5); }
  50% { box-shadow: 0 0 15px rgba(255, 152, 0, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 152, 0, 0.5); }
}

/* 光标指示器动画 */
@keyframes cursor-blink {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 响应式设计 - 完全重写 */
@media (max-width: 100%) {
  #markdown-content {
    max-width: 1000px;
  }
}

@media (max-width: 768px) {
  #content-container {
    padding: 15px;
  }

  #toc-container {
    width: 220px;
  }

  .container.with-toc #content-container {
    padding-left: 240px; /* 220px目录宽度 + 20px内边距 */
  }

  #markdown-content {
    max-width: 100%;
  }

  /* 在中等屏幕上调整Mermaid图表 */
  .mermaid {
    width: 95%; /* 在中等屏幕上使用更大的宽度 */
    max-width: 95%;
  }

  .mermaid-container {
    padding: 15px;
    min-height: 180px;
  }
}

@media (max-width: 576px) {
  /* 在小屏幕上，目录覆盖内容而不是推动内容 */
  #toc-container {
    width: 80%;
    box-shadow: 0 0 15px rgba(0,0,0,0.2);
  }

  .container.with-toc #content-container {
    padding-left: 20px; /* 恢复正常内边距 */
    padding-right: 20px;
  }

  #markdown-content {
    max-width: 100%;
  }

  /* 在小屏幕上调整Mermaid图表 */
  .mermaid {
    width: 98%; /* 在小屏幕上使用几乎全宽 */
    max-width: 98%;
  }

  .mermaid-container {
    padding: 10px;
    min-height: 150px;
    margin: 12px 0;
  }

  /* 添加半透明背景，当目录打开时 */
  .container.with-toc::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 90;
    display: block;
  }

  /* 确保目录在半透明背景之上 */
  #toc-container {
    z-index: 100;
  }
}
