# 更新日志

## 0.1.56 (2024-05-29)

### 新增功能

- ✨ **Mermaid图表支持**: 添加了完整的Mermaid图表渲染功能
  - 支持流程图、序列图、甘特图、类图、状态图、饼图等所有主要图表类型
  - 实时预览和同步更新
  - 错误处理和用户友好的错误显示
  - 响应式设计，适配不同屏幕尺寸

### 技术改进

- 🔧 新增 `mermaidPlugin.ts` 插件模块
- 🔧 集成 Mermaid.js v10.6.1 库
- 🔧 扩展前端渲染逻辑支持图表渲染
- 🔧 添加专用的Mermaid样式和错误处理样式

### 文档更新

- 📚 更新 README.md 添加Mermaid使用说明
- 📚 新增 MERMAID_SUPPORT.md 详细文档
- 📚 创建 test-mermaid.md 测试用例

## 0.1.50 (2024-05-11)

### 安全更新

- 添加SVG安全过滤功能，防止恶意SVG内容执行脚本
- 移除SVG标签中的危险属性，包括事件处理属性和远程资源链接
- 移除可能导致安全问题的HTML元素，如script、iframe等

### 改进

- 更新发布配置，优化插件包内容
- 添加.vscodeignore文件，排除不必要的文件

## 0.1.49 (2023-12-15)

### 功能

- 改进目录导航样式
- 优化Markdown预览性能
- 修复WebSocket连接问题

## 0.1.48 (2023-11-20)

### 功能

- 初始版本发布
- 支持实时Markdown预览
- 支持目录导航
- 光标位置同步
- 代码高亮